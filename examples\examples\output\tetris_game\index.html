```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tetris Game</title>
    <style>
        /* General Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background-color: #f0f0f0;
        }

        /* Game Container */
        .game-container {
            display: flex;
            flex-direction: row;
            gap: 20px;
            background-color: #fff;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        @media (max-width: 768px) {
            .game-container {
                flex-direction: column;
            }
        }

        /* Game Board */
        .game-board {
            display: grid;
            grid-template-columns: repeat(10, 30px);
            grid-template-rows: repeat(20, 30px);
            gap: 1px;
            background-color: #333;
            border: 2px solid #333;
        }

        .cell {
            width: 30px;
            height: 30px;
            background-color: #fff;
        }

        /* Sidebar */
        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 20px;
            width: 150px;
        }

        /* Score Display */
        .score-display {
            padding: 10px;
            background-color: #333;
            color: #fff;
            border-radius: 5px;
            text-align: center;
        }

        .score-title {
            font-size: 1.2rem;
            margin-bottom: 5px;
        }

        .score-value {
            font-size: 1.5rem;
            font-weight: bold;
        }

        /* Next Piece Preview */
        .next-piece {
            padding: 10px;
            background-color: #333;
            border-radius: 5px;
            text-align: center;
        }

        .next-title {
            font-size: 1.2rem;
            color: #fff;
            margin-bottom: 10px;
        }

        .next-grid {
            display: grid;
            grid-template-columns: repeat(4, 30px);
            grid-template-rows: repeat(4, 30px);
            gap: 1px;
            margin: 0 auto;
        }

        .next-cell {
            width: 30px;
            height: 30px;
            background-color: #fff;
        }
    </style>
</head>
<body>
    <div class="game-container">
        <!-- Game Board -->
        <div class="game-board" id="game-board">
            <!-- Cells will be generated dynamically with JavaScript -->
        </div>

        <!-- Sidebar -->
        <div class="sidebar">
            <!-- Score Display -->
            <div class="score-display">
                <div class="score-title">Score</div>
                <div class="score-value" id="score">0</div>
            </div>

            <!-- Next Piece Preview -->
            <div class="next-piece">
                <div class="next-title">Next Piece</div>
                <div class="next-grid" id="next-piece">
                    <!-- Next piece cells will be generated dynamically with JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // Game Constants
            const COLS = 10;
            const ROWS = 20;
            const BLOCK_SIZE = 30;
            const NEXT_BOARD_SIZE = 4;
            const EMPTY = 'white';
            
            // Tetromino shapes with their different rotations
            const SHAPES = {
                I: [
                    [0, 0, 0, 0],
                    [1, 1, 1, 1],
                    [0, 0, 0, 0],
                    [0, 0, 0, 0]
                ],
                J: [
                    [1, 0, 0],
                    [1, 1, 1],
                    [0, 0, 0]
                ],
                L: [
                    [0, 0, 1],
                    [1, 1, 1],
                    [0, 0, 0]
                ],
                O: [
                    [1, 1],
                    [1, 1]
                ],
                S: [
                    [0, 1, 1],
                    [1, 1, 0],
                    [0, 0, 0]
                ],
                T: [
                    [0, 1, 0],
                    [1, 1, 1],
                    [0, 0, 0]
                ],
                Z: [
                    [1, 1, 0],
                    [0, 1, 1],
                    [0, 0, 0]
                ]
            };

            // Colors for each block type
            const COLORS = {
                I: 'cyan',
                O: 'yellow',
                T: 'purple',
                L: 'orange',
                J: 'blue',
                S: 'green',
                Z: 'red'
            };

            // Game Variables
            let board = createBoard();
            let currentPiece = null;
            let nextPiece = null;
            let score = 0;
            let isGameOver = false;
            let gameSpeed = 1000; // Initial speed in ms
            let gameInterval = null;

            // DOM Elements
            const gameBoard = document.getElementById('game-board');
            const nextPieceGrid = document.getElementById('next-piece');
            const scoreDisplay = document.getElementById('score');

            // Initialize the game
            init();

            function init() {
                createBoardUI();
                createNextPieceUI();
                generateNewPiece();
                generateNextPiece();
                updateScore();
                startGame();
                addEventListeners();
            }

            function createBoard() {
                return Array.from({ length: ROWS }, () => Array(COLS).fill(EMPTY));
            }

            function createBoardUI() {
                gameBoard.innerHTML = '';
                for (let r = 0; r < ROWS; r++) {
                    for (let c = 0; c < COLS; c++) {
                        const cell = document.createElement('div');
                        cell.className = 'cell';
                        cell.id = `${r}-${c}`;
                        gameBoard.appendChild