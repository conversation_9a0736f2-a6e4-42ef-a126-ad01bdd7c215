# **EvoAgentX**

<p align="center" style="font-size: 1.0rem;">
  <em>An automated framework for evaluating and evolving agentic workflows.</em>
</p>

<p align="center">
  <img src="./assets/framework_en.jpg">
</p>


## 🚀 Introduction

EvoAgentX is an open-source framework designed to automate the generation, execution, evaluation and optimization of agentic workflows. By leveraging large language models (LLMs), EvoAgentX enables developers and researchers to prototype, test, and deploy multi-agent systems that grow in complexity and capability over time. 

## ✨ Key Features

- **Easy Agent and Workflow Customization**: Easily create customized agents and workflows using natural language prompts. EvoAgentX makes it easy to turn your high-level ideas to working systems. 
- **Automatic Workflow Generation & Execution**: Automatically generate and execute agentic workflows from simple goal descriptions, reducing manual workload in multi-agent system design. 
- **WorkFlow Optimization**: Integrates existing workflow optimization techniques that iteratively refine workflows for improved performance. 
- **Benchmarking & Evaluation**: Includes built-in benchmarks and standardized evaluation metrics to measure workflow. effectiveness across different tasks and agent configurations 
- **Workflow Execution Toolkit**: Offers a suite of tools essential for executing complex workflows, such as search components and the Model Context Protocol (MCP). 

## 🔍 How It Works

EvoAgentX uses a modular architecture with the following core components:

1. **Workflow Generator**: Creates agentic workflows based on your goals
2. **Agent Manager**: Handles agent creation, configuration, and deployment
3. **Workflow Executor**: Runs workflows efficiently with proper agent communication
4. **Evaluators**: Provides performance metrics and improvement suggestions
5. **Optimizers**: Evolves workflows to enhance performance over time


## 👥 Community

- **Discord**: Join our [Discord Channel](https://discord.gg/w3x2YrCa) for discussions and support
- **GitHub**: Contribute to the project on [GitHub](https://github.com/EvoAgentX/EvoAgentX/)
- **Email**: Contact us at [<EMAIL>](mailto:<EMAIL>)
- **WeChat**: Connect with us on [WeChat](https://github.com/EvoAgentX/EvoAgentX/blob/main/assets/wechat_info.md) for updates and support.

## 🤝 Contributing

We welcome contributions from the community! Please refer to our [Contributing Guidelines](https://github.com/EvoAgentX/EvoAgentX/blob/main/CONTRIBUTING.md) for more details.
