import os  
from dotenv import load_dotenv  
from evoagentx.models import SiliconFlowConfig, SiliconFlowLLM  
  
# 加载环境变量  
load_dotenv()  
SILICONFLOW_API_KEY = os.getenv("SILICONFLOW_API_KEY")  
  
# 配置 SiliconFlow 模型  
config = SiliconFlowConfig(  
    model="deepseek-ai/DeepSeek-V3",  # 使用 SiliconFlow 平台的模型命名  
    siliconflow_key=SILICONFLOW_API_KEY,  
    temperature=0.7,  
    max_tokens=1000,  
    stream=True  # 可选：启用流式响应  
)  
  
# 初始化模型  
llm = SiliconFlowLLM(config=config)  
  
from evoagentx.workflow import WorkFlowGenerator, WorkFlow  
from evoagentx.agents import AgentManager  
  
# 定义目标任务  
goal = "分析一下智能体框架的发展趋势"
  
# 生成工作流  
wf_generator = WorkFlowGenerator(llm=llm)  
workflow_graph = wf_generator.generate_workflow(goal=goal)  
  
# 创建智能体管理器  
agent_manager = AgentManager()  
agent_manager.add_agents_from_workflow(workflow_graph, llm_config=config)  
  
# 创建并执行工作流  
workflow = WorkFlow(graph=workflow_graph, agent_manager=agent_manager, llm=llm)  
output = workflow.execute()
print(output)