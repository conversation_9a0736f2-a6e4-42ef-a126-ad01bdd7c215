name: style

on:
  push: {branches: [main]} # pushes to main
  pull_request: {} # all PRs

jobs:
  ruff:
    strategy:
      matrix:
        os: ['ubuntu-latest']
        python-version: ['3.10', '3.12']

    runs-on: ${{ matrix.os }}
    env:
      runtag: ${{ matrix.os }}-${{ matrix.python-version }}

    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Install Python ${{ matrix.python-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}

    - name: Verify Python Version
      run: python --version

    - name: Install Dependencies
      run: |
        python -m pip install --upgrade pip 
        pip install -r requirements.txt
        pip install -e .

    - name: Ruff
      run: 'ruff check --output-format=github evoagentx'